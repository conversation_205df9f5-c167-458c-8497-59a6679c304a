import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { fetchCurrentUser } from '../../store/slices/userSlice';
import Sidebar from '../../components/layout/Sidebar';
import Header from '../../components/layout/Header';
import Banner from '../../components/layout/Banner';
import { sidebarConfigs } from '../../config/sidebarConfigs';
import InstituteApprovalGuard from '../../components/auth/InstituteApprovalGuard';

// Dashboard components
import AdminDashboard from '../admin/AdminDashboard';
import StudentDashboard from '../student/StudentDashboard';
import TeacherDashboard from '../teacher/TeacherDashboard';
import MentorDashboard from '../mentor/MentorDashboard';
import MentorSettings from '../mentor/MentorSettings';
import InstituteDashboard from '../admin/InstituteDashboard';
import SponsorDashboard from '../admin/SponsorDashboard';

// Teacher components
import TeacherClasses from '../teacher/TeacherClasses';
import Classroom from '../teacher/Classroom';
import ClassInfo from '../teacher/ClassInfo';
import TeacherSettings from '../teacher/TeacherSettings';
import TeacherExams from '../teacher/TeacherExams';
import TeacherCreateExam from '../teacher/TeacherCreateExam';
import TeacherExamDetail from '../teacher/TeacherExamDetail';
import TeacherClassroom from '../teacher/TeacherClassroom';
import TeacherTasks from '../teacher/TeacherTasks';
import TeacherTaskDetail from '../teacher/TeacherTaskDetail';
import CreateTask from '../teacher/CreateTask';
import EditTask from '../teacher/EditTask';
import TeacherMentorshipPage from '../teacher/TeacherMentorshipPage';
import MonitoringDashboard from '../../components/exam/teacher/MonitoringDashboard/MonitoringDashboard';

// Admin components
import AdminUsers from '../admin/Users';
import AdminSettings from '../admin/AdminSettings';
import InstituteApprovals from '../admin/InstituteApprovals';
import AdminExams from '../admin/AdminExams';
import Material from '../admin/Material';
import Plans from '../admin/Plans';


// Student components
import StudentClasses from '../student/StudentClasses';
import StudentSettings from '../student/StudentSettings';
import StudentExams from '../student/StudentExams';
import StudentTakeExam from '../student/StudentTakeExam';
import StudentExamResults from '../student/StudentExamResults';
import ExamSubmissionSuccess from '../student/ExamSubmissionSuccess';
import StudentClassroom from '../student/StudentClassroom';
import StudentTasks from '../student/StudentTasks';
import StudentTasksAll from '../student/StudentTasksAll';
import StudentTaskDetail from '../student/StudentTaskDetail';

// Import the advanced exam interface
import ExamInterface from '../../components/exam/student/ExamInterface/ExamInterface';

// Test components
import TimezoneTest from '../TimezoneTest';
import DatePickerTest from '../DatePickerTest';
import WebSocketTest from '../student/WebSocketTest';

// New system components
import EventsPage from '../events/EventsPage';
import CreateEventPage from '../events/CreateEventPage';
import MentorsPage from '../mentors/MentorsPage';

import CompetitionsPage from '../competitions/CompetitionsPage';

// Institute components
import InstituteSettings from '../admin/InstituteSettings';
import InstituteEvents from '../admin/InstituteEvents';
import InstituteMentors from '../institute/InstituteMentors';

// Sponsor components
import SponsorInstitutes from '../admin/SponsorInstitutes';
import SponsorFunding from '../admin/SponsorFunding';
import SponsorSettings from '../admin/SponsorSettings';

// Component mapping for different user types and sections
const componentMap = {
  admin: {
    dashboard: AdminDashboard,
    settings: AdminSettings,
    users: AdminUsers,
    'institute-approvals': InstituteApprovals,
    exams: AdminExams,
    material: Material,
    plans: Plans,
    events: EventsPage,
  },
  student: {
    dashboard: StudentDashboard,
    settings: StudentSettings,
    classes: StudentClasses,
    classroom: StudentClassroom,
    exams: StudentExams,
    'take-exam': StudentTakeExam, // Use the simple exam interface for now
    'exam-results': StudentExamResults,
    'exam-submitted': ExamSubmissionSuccess,
    tasks: StudentTasks,
    'tasks/all': StudentTasksAll,
    task: StudentTaskDetail,
    events: EventsPage,
    mentors: MentorsPage,
    'timezone-test': TimezoneTest, // Test page for timezone functionality
    'datepicker-test': DatePickerTest, // Test page for improved date picker
    'websocket-test': WebSocketTest, // Test page for WebSocket functionality
  },
  teacher: {
    dashboard: TeacherDashboard,
    settings: TeacherSettings,
    classes: Classroom,
    classroom: TeacherClassroom,
    'classroom-old': ClassInfo,
    exams: TeacherExams,
    'create-exam': TeacherCreateExam,
    exam: TeacherExamDetail,
    tasks: TeacherTasks,
    task: TeacherTaskDetail,
    'create-task': CreateTask,
    'edit-task': EditTask,
    events: EventsPage,
    competitions: CompetitionsPage,
    mentorship: TeacherMentorshipPage,
  },
  mentor: {
    dashboard: MentorDashboard,
    settings: MentorSettings,
  },
  institute: {
    dashboard: InstituteDashboard,
    settings: InstituteSettings,
    events: InstituteEvents,
    'events/create': CreateEventPage,
    mentors: InstituteMentors,
  },
  sponsor: {
    dashboard: SponsorDashboard,
    settings: SponsorSettings,
    institutes: SponsorInstitutes,
    funding: SponsorFunding,
  },
};

// URL redirects for different user types
const userTypeRedirects = {
  '/Admin': '/admin/dashboard',
  '/Student': '/student/dashboard',
  '/Teacher': '/teacher/dashboard',
  '/Institute': '/institute/dashboard',
  '/Sponsor': '/sponsor/dashboard',
  '/Mentor': '/mentor/dashboard',
  '/admin': '/admin/dashboard',
  '/student': '/student/dashboard',
  '/teacher': '/teacher/dashboard',
  '/institute': '/institute/dashboard',
  '/sponsor': '/sponsor/dashboard',
  '/mentor': '/mentor/dashboard',
};

// Helper function to check if a string is an API endpoint name that shouldn't be treated as an ID
const isApiEndpointName = (name) => {
  const apiEndpointNames = ['my-exams', 'upcoming', 'student', 'teacher', 'admin'];
  return apiEndpointNames.includes(name);
};

function DashboardLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isAuthChecking, setIsAuthChecking] = useState(true);
  
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentUser, status, error } = useSelector((state) => state.users);

  // Extract user type and section from URL path
  const pathSegments = location.pathname.split('/');
  const userType = pathSegments[1];
  const currentSection = pathSegments[2] || 'dashboard';

  // Clear any corrupted localStorage data on component mount
  useEffect(() => {
    try {
      const storedUserData = localStorage.getItem("userdata");
      if (storedUserData) {
        JSON.parse(storedUserData); // Test if it's valid JSON
      }
    } catch (error) {
      console.warn('Corrupted user data in localStorage, clearing...', error);
      localStorage.removeItem("userdata");
    }
  }, []);
  const subSection = pathSegments[3];
  const subSubSection = pathSegments[4];



  // Handle URL redirects
  useEffect(() => {
    const redirectPath = userTypeRedirects[location.pathname];
    if (redirectPath) {
      navigate(redirectPath);
    }
  }, [location.pathname, navigate]);

  // Check authentication and fetch current user data
  useEffect(() => {
    let didCancel = false;
    const token = localStorage.getItem("token");
    const role = localStorage.getItem("role");

    if (!token || !role) {
      console.log('No token or role found in dashboard, redirecting to login');
      navigate('/Login');
      return;
    }

    if (!currentUser) {
      localStorage.removeItem("userdata");
      dispatch(fetchCurrentUser())
        .finally(() => {
          if (!didCancel) setIsAuthChecking(false);
        });
    } else {
      setIsAuthChecking(false);
    }

    return () => { didCancel = true; };
  }, [dispatch, currentUser, navigate]);

  // Helper function to clean user data for localStorage
  const cleanUserDataForStorage = (userData) => {
    if (!userData) return userData;

    const cleaned = { ...userData };

    // Remove large base64 image data objects that can cause quota issues
    if (cleaned.profile_picture_data) {
      delete cleaned.profile_picture_data;
    }

    if (cleaned.mentor_profile?.profile_picture_data) {
      cleaned.mentor_profile = { ...cleaned.mentor_profile };
      delete cleaned.mentor_profile.profile_picture_data;
    }

    return cleaned;
  };

  // Store user data in localStorage (excluding large image data)
  useEffect(() => {
    if (currentUser) {
      try {
        const userDataForStorage = cleanUserDataForStorage(currentUser);
        localStorage.setItem("userdata", JSON.stringify(userDataForStorage));
      } catch (error) {
        console.warn('Failed to store user data in localStorage:', error);
        // If storage fails, try storing minimal user data
        try {
          const minimalUserData = {
            id: currentUser.id,
            username: currentUser.username,
            email: currentUser.email,
            user_type: currentUser.user_type,
            profile_picture_url: currentUser.profile_picture_url,
            profile_picture_thumbnail_url: currentUser.profile_picture_thumbnail_url
          };
          localStorage.setItem("userdata", JSON.stringify(minimalUserData));
        } catch (fallbackError) {
          console.error('Failed to store even minimal user data:', fallbackError);
          // Clear localStorage if it's corrupted
          localStorage.removeItem("userdata");
        }
      }
    }
  }, [currentUser]);

  // Handle authentication errors
  useEffect(() => {
    if (error && status === 'failed') {
      console.log('Error fetching current user, redirecting to login');
      localStorage.removeItem('token');
      localStorage.removeItem('role');
      localStorage.removeItem('userdata');
      navigate('/Login');
    } else if (status === 'succeeded' && currentUser) {
      setIsAuthChecking(false);
    }
  }, [error, status, navigate, currentUser]);

  // Role-based access control
  useEffect(() => {
    if (currentUser && !isAuthChecking) {
      const userRole = currentUser.user_type;
      const pathRole = location.pathname.split('/')[1];
      
      if (userRole && pathRole && userRole.toLowerCase() !== pathRole.toLowerCase()) {
        console.log(`Access denied: User role ${userRole} trying to access ${pathRole} dashboard`);
        navigate(`/${userRole.toLowerCase()}/dashboard`, { replace: true });
      }
    }
  }, [currentUser, isAuthChecking, location.pathname, navigate]);

  // Get sidebar config for current user type
  const sidebarConfig = sidebarConfigs[userType] || [];

  // Get component to render
  const getComponentToRender = () => {
    const userComponents = componentMap[userType];

    if (!userComponents) {
      return (
        <div className="text-center py-8">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Invalid user type: {userType}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Available user types: {Object.keys(componentMap).join(', ')}
          </p>
        </div>
      );
    }

    // First try to find component with nested path (e.g., "events/create")
    let Component = null;
    let sectionKey = currentSection;

    if (subSection) {
      const nestedSectionKey = `${currentSection}/${subSection}`;
      Component = userComponents[nestedSectionKey];
      if (Component) {
        sectionKey = nestedSectionKey;
      }
    }

    // If no nested component found, try the regular section
    if (!Component) {
      Component = userComponents[currentSection];
    }

    if (!Component) {
      return (
        <div className="text-center py-8">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            Page not found: {currentSection}{subSection ? `/${subSection}` : ''}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Available sections for {userType}: {Object.keys(userComponents).join(', ')}
          </p>
        </div>
      );
    }

    // Handle classroom-specific component
    if (currentSection === "classroom" && subSection) {
      // Use different components based on user type
      if (userType === "teacher") {
        return <ClassInfo classroomId={subSection} />;
      } else if (userType === "student") {
        const StudentClassroomComponent = userComponents['classroom'];
        return StudentClassroomComponent ? <StudentClassroomComponent classroomId={subSection} /> : null;
      }
    }

    // Handle exam-specific components with IDs (but not for API endpoint names)
    if (currentSection === "take-exam" && subSection && !isApiEndpointName(subSection)) {
      const TakeExamComponent = userComponents['take-exam'];
      return TakeExamComponent ? <TakeExamComponent /> : null;
    }

    if (currentSection === "exam-results" && subSection && !isApiEndpointName(subSection)) {
      const ExamResultsComponent = userComponents['exam-results'];
      return ExamResultsComponent ? <ExamResultsComponent /> : null;
    }

    // Handle teacher exam detail with ID (but not for API endpoint names)
    if (currentSection === "exam" && subSection && !isApiEndpointName(subSection) && userType === "teacher") {
      // Handle edit route: /teacher/exam/{examId}/edit
      if (subSubSection === "edit") {
        const EditExamComponent = userComponents['create-exam']; // Reuse create exam component for editing
        return EditExamComponent ? <EditExamComponent /> : null;
      }

      const ExamDetailComponent = userComponents['exam'];
      return ExamDetailComponent ? <ExamDetailComponent /> : null;
    }

    // Redirect incorrect exam URLs to the correct exams list page
    if (currentSection === "exam" && subSection && isApiEndpointName(subSection) && userType === "teacher") {
      navigate('/teacher/exams', { replace: true });
      return null;
    }

    // Redirect incorrect student exam URLs
    if ((currentSection === "take-exam" || currentSection === "exam-results") && subSection && isApiEndpointName(subSection) && userType === "student") {
      navigate('/student/exams', { replace: true });
      return null;
    }

    // Handle teacher edit exam with ID
    if (currentSection === "edit-exam" && subSection && userType === "teacher") {
      const EditExamComponent = userComponents['create-exam']; // Reuse create exam component for editing
      return EditExamComponent ? <EditExamComponent /> : null;
    }

    // Handle task-specific components with IDs
    if (currentSection === "task" && subSection && !isApiEndpointName(subSection)) {
      // Handle edit route: /teacher/task/{taskId}/edit
      if (subSubSection === "edit" && userType === "teacher") {
        const EditTaskComponent = userComponents['edit-task']; // Use dedicated edit task component
        return EditTaskComponent ? <EditTaskComponent taskId={subSection} /> : null;
      }

      const TaskDetailComponent = userComponents['task'];
      return TaskDetailComponent ? <TaskDetailComponent taskId={subSection} /> : null;
    }

    // Redirect incorrect task URLs to the correct tasks list page
    if (currentSection === "task" && subSection && isApiEndpointName(subSection)) {
      const tasksPath = userType === "teacher" ? '/teacher/tasks' : '/student/tasks';
      navigate(tasksPath, { replace: true });
      return null;
    }

    return <Component />;
  };

  // Handle navigation from sidebar
  const handleNavigation = (path) => {
    navigate(path);
  };

  // Update sidebar config with navigation handler
  const sidebarConfigWithNavigation = sidebarConfig.map(item => ({
    ...item,
    onClick: () => handleNavigation(item.path)
  }));

  // Show loading spinner while checking authentication
  if (isAuthChecking || status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        sidebarOpen={sidebarOpen}
        setSidebarOpen={setSidebarOpen}
        config={sidebarConfigWithNavigation}
      />

      {/* Content area */}
      <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
        {/* Site header */}
        <Header sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />

        <main className="grow">
          <div className="px-4 sm:px-6 lg:px-8 py-8 w-full max-w-9xl mx-auto">
            {userType === 'institute' ? (
              <InstituteApprovalGuard>
                {getComponentToRender()}
              </InstituteApprovalGuard>
            ) : (
              getComponentToRender()
            )}
          </div>
        </main>

        <Banner />
      </div>
    </div>
  );
}

export default DashboardLayout; 