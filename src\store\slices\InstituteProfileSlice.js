import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import API_BASE_URL from "../../utils/api/API_URL";

const BASE_URL = `${API_BASE_URL}/api/institutes`;
const getAuthToken = () => localStorage.getItem("token");

// Async Thunks

// Fetch institute profile
export const fetchInstituteProfile = createAsyncThunk(
  "instituteProfile/fetchProfile",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/profile`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });

      // Check if the response contains an error (API returns 200 with error details)
      if (res.data?.detail?.error === 'PROFILE_NOT_CREATED' || res.data?.detail?.error === 'PROFILE_NOT_FOUND') {
        return thunkAPI.rejectWithValue({
          ...res.data,
          isProfileNotFound: true
        });
      }

      return res.data;
    } catch (err) {
      const errorData = err.response?.data || err.message;

      // Handle specific PROFILE_NOT_FOUND error (for backward compatibility)
      if (errorData?.detail?.error === 'PROFILE_NOT_FOUND' || errorData?.detail?.error === 'PROFILE_NOT_CREATED') {
        return thunkAPI.rejectWithValue({
          ...errorData,
          isProfileNotFound: true
        });
      }

      return thunkAPI.rejectWithValue(errorData);
    }
  }
);

// Create/Update institute profile
export const saveInstituteProfile = createAsyncThunk(
  "instituteProfile/saveProfile",
  async (profileData, thunkAPI) => {
    try {
      const res = await axios.put(`${BASE_URL}/profile`, profileData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "application/json"
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Create/Update institute profile with documents
export const saveInstituteProfileWithDocuments = createAsyncThunk(
  "instituteProfile/saveProfileWithDocuments",
  async ({ profileData, documents }, thunkAPI) => {
    try {
      const formData = new FormData();

      // Add profile fields to FormData
      Object.keys(profileData).forEach(key => {
        if (profileData[key] !== null && profileData[key] !== undefined) {
          formData.append(key, profileData[key]);
        }
      });

      // Add documents to FormData
      documents.forEach(doc => {
        if (doc.file) {
          formData.append('document_files', doc.file);
          formData.append('document_types', doc.type || 'other');
          formData.append('document_descriptions', doc.description || '');
        }
      });

      const res = await axios.put(`${BASE_URL}/profile/with-documents`, formData, {
        headers: {
          Authorization: `Bearer ${getAuthToken()}`,
          "Content-Type": "multipart/form-data"
        },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch profile completion status
export const fetchProfileStatus = createAsyncThunk(
  "instituteProfile/fetchProfileStatus",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/profile/status`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Submit profile for admin approval
export const submitForApproval = createAsyncThunk(
  "instituteProfile/submitForApproval",
  async (_, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/profile/submit-for-verification`, {}, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Admin actions - Approve institute profile
export const approveInstituteProfile = createAsyncThunk(
  "instituteProfile/approveProfile",
  async ({ instituteId, approvalData }, thunkAPI) => {
    try {
      const res = await axios.post(`${API_BASE_URL}/api/admin/institutes/${instituteId}/approve`, approvalData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Admin actions - Reject institute profile
export const rejectInstituteProfile = createAsyncThunk(
  "instituteProfile/rejectProfile",
  async ({ instituteId, rejectionData }, thunkAPI) => {
    try {
      const res = await axios.post(`${API_BASE_URL}/api/admin/institutes/${instituteId}/reject`, rejectionData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch pending institute approvals (Admin only)
export const fetchPendingInstitutes = createAsyncThunk(
  "instituteProfile/fetchPendingInstitutes",
  async (_, thunkAPI) => {
    try {
      const res = await axios.get(`${API_BASE_URL}/api/admin/institutes/pending`, {
        headers: { Authorization: `Bearer ${getAuthToken()}` },
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Initial State
const initialState = {
  profile: null,
  profileLoading: false,
  profileError: null,
  profileNotFound: false, // Flag to track when profile doesn't exist

  saveLoading: false,
  saveError: null,
  saveSuccess: false,

  submitLoading: false,
  submitError: null,
  submitSuccess: false,

  // Admin states
  pendingInstitutes: [],
  pendingLoading: false,
  pendingError: null,

  approveLoading: false,
  approveError: null,
  approveSuccess: false,

  rejectLoading: false,
  rejectError: null,
  rejectSuccess: false,

  // Profile completion status
  isProfileComplete: false,
  approvalStatus: 'draft', // 'draft', 'pending', 'approved', 'rejected'
  rejectionReason: null,
  approvalDate: null,
  rejectionDate: null
};

// Slice
const instituteProfileSlice = createSlice({
  name: "instituteProfile",
  initialState,
  reducers: {
    clearErrors: (state) => {
      state.profileError = null;
      state.saveError = null;
      state.submitError = null;
      state.pendingError = null;
      state.approveError = null;
      state.rejectError = null;
      state.profileNotFound = false;
    },
    clearSuccessStates: (state) => {
      state.saveSuccess = false;
      state.submitSuccess = false;
      state.approveSuccess = false;
      state.rejectSuccess = false;
    },
    updateProfileField: (state, action) => {
      const { field, value } = action.payload;
      if (state.profile) {
        state.profile[field] = value;
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Profile
      .addCase(fetchInstituteProfile.pending, (state) => {
        state.profileLoading = true;
        state.profileError = null;
        state.profileNotFound = false;
      })
      .addCase(fetchInstituteProfile.fulfilled, (state, action) => {
        state.profileLoading = false;
        state.profileNotFound = false;
        // Handle new API response structure
        const responseData = action.payload;
        state.profile = responseData.profile;
        state.approvalStatus = responseData.verification_status || responseData.profile?.verification_status || 'draft';
        state.rejectionReason = responseData.profile?.verification_notes;
        state.approvalDate = responseData.profile?.verified_at;
        state.isProfileComplete = !!responseData.profile?.institute_name; // Basic check for profile completion
      })
      .addCase(fetchInstituteProfile.rejected, (state, action) => {
        state.profileLoading = false;
        state.profileError = action.payload;

        // Check if this is a PROFILE_NOT_FOUND error
        if (action.payload?.isProfileNotFound) {
          state.profileNotFound = true;
          state.approvalStatus = 'not_created';
          state.isProfileComplete = false;
        }
      })

      // Fetch Profile Status
      .addCase(fetchProfileStatus.pending, (state) => {
        state.profileLoading = true;
        state.profileError = null;
      })
      .addCase(fetchProfileStatus.fulfilled, (state, action) => {
        state.profileLoading = false;
        state.approvalStatus = action.payload || 'draft';
      })
      .addCase(fetchProfileStatus.rejected, (state, action) => {
        state.profileLoading = false;
        state.profileError = action.payload;
      })

      // Save Profile
      .addCase(saveInstituteProfile.pending, (state) => {
        state.saveLoading = true;
        state.saveError = null;
        state.saveSuccess = false;
      })
      .addCase(saveInstituteProfile.fulfilled, (state, action) => {
        state.saveLoading = false;
        state.saveSuccess = true;
        // Handle new API response structure
        const responseData = action.payload;
        state.profile = responseData.profile;
        state.approvalStatus = responseData.verification_status || responseData.profile?.verification_status || 'draft';
        state.isProfileComplete = !!responseData.profile?.institute_name;
      })
      .addCase(saveInstituteProfile.rejected, (state, action) => {
        state.saveLoading = false;
        state.saveError = action.payload;
      })

      // Save Profile with Documents
      .addCase(saveInstituteProfileWithDocuments.pending, (state) => {
        state.saveLoading = true;
        state.saveError = null;
        state.saveSuccess = false;
      })
      .addCase(saveInstituteProfileWithDocuments.fulfilled, (state, action) => {
        state.saveLoading = false;
        state.saveSuccess = true;
        // Handle new API response structure
        const responseData = action.payload;
        state.profile = responseData.profile;
        state.approvalStatus = responseData.verification_status || responseData.profile?.verification_status || 'draft';
        state.isProfileComplete = !!responseData.profile?.institute_name;
      })
      .addCase(saveInstituteProfileWithDocuments.rejected, (state, action) => {
        state.saveLoading = false;
        state.saveError = action.payload;
      })

      // Submit for Approval
      .addCase(submitForApproval.pending, (state) => {
        state.submitLoading = true;
        state.submitError = null;
        state.submitSuccess = false;
      })
      .addCase(submitForApproval.fulfilled, (state, action) => {
        state.submitLoading = false;
        state.submitSuccess = true;
        state.approvalStatus = 'pending';
      })
      .addCase(submitForApproval.rejected, (state, action) => {
        state.submitLoading = false;
        state.submitError = action.payload;
      })

      // Fetch Pending Institutes (Admin)
      .addCase(fetchPendingInstitutes.pending, (state) => {
        state.pendingLoading = true;
        state.pendingError = null;
      })
      .addCase(fetchPendingInstitutes.fulfilled, (state, action) => {
        state.pendingLoading = false;
        state.pendingInstitutes = action.payload;
      })
      .addCase(fetchPendingInstitutes.rejected, (state, action) => {
        state.pendingLoading = false;
        state.pendingError = action.payload;
      })

      // Approve Institute (Admin)
      .addCase(approveInstituteProfile.pending, (state) => {
        state.approveLoading = true;
        state.approveError = null;
        state.approveSuccess = false;
      })
      .addCase(approveInstituteProfile.fulfilled, (state, action) => {
        state.approveLoading = false;
        state.approveSuccess = true;
        // Update the institute in pending list
        state.pendingInstitutes = state.pendingInstitutes.filter(
          institute => institute.id !== action.payload.id
        );
      })
      .addCase(approveInstituteProfile.rejected, (state, action) => {
        state.approveLoading = false;
        state.approveError = action.payload;
      })

      // Reject Institute (Admin)
      .addCase(rejectInstituteProfile.pending, (state) => {
        state.rejectLoading = true;
        state.rejectError = null;
        state.rejectSuccess = false;
      })
      .addCase(rejectInstituteProfile.fulfilled, (state, action) => {
        state.rejectLoading = false;
        state.rejectSuccess = true;
        // Update the institute in pending list
        state.pendingInstitutes = state.pendingInstitutes.filter(
          institute => institute.id !== action.payload.id
        );
      })
      .addCase(rejectInstituteProfile.rejected, (state, action) => {
        state.rejectLoading = false;
        state.rejectError = action.payload;
      });
  },
});

export const { clearErrors, clearSuccessStates, updateProfileField } = instituteProfileSlice.actions;

// Selectors
export const selectProfile = (state) => state.instituteProfile.profile;
export const selectProfileLoading = (state) => state.instituteProfile.profileLoading;
export const selectProfileError = (state) => state.instituteProfile.profileError;
export const selectProfileNotFound = (state) => state.instituteProfile.profileNotFound;
export const selectSaveLoading = (state) => state.instituteProfile.saveLoading;
export const selectSaveError = (state) => state.instituteProfile.saveError;
export const selectSaveSuccess = (state) => state.instituteProfile.saveSuccess;
export const selectSubmitLoading = (state) => state.instituteProfile.submitLoading;
export const selectSubmitError = (state) => state.instituteProfile.submitError;
export const selectSubmitSuccess = (state) => state.instituteProfile.submitSuccess;
export const selectApprovalStatus = (state) => state.instituteProfile.approvalStatus;
export const selectIsProfileComplete = (state) => state.instituteProfile.isProfileComplete;
export const selectRejectionReason = (state) => state.instituteProfile.rejectionReason;

// Admin selectors
export const selectPendingInstitutes = (state) => state.instituteProfile.pendingInstitutes;
export const selectPendingLoading = (state) => state.instituteProfile.pendingLoading;
export const selectPendingError = (state) => state.instituteProfile.pendingError;
export const selectApproveLoading = (state) => state.instituteProfile.approveLoading;
export const selectApproveSuccess = (state) => state.instituteProfile.approveSuccess;
export const selectRejectLoading = (state) => state.instituteProfile.rejectLoading;
export const selectRejectSuccess = (state) => state.instituteProfile.rejectSuccess;

export default instituteProfileSlice.reducer;
