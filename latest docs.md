# Institute Document Upload API

This document describes how to use the Institute profile creation/update API with document uploading capabilities.

## Overview

The Institute profile API now supports document uploading for verification purposes. There are two main endpoints:

1. **PUT /api/institutes/profile** - JSON-only profile updates (existing)
2. **PUT /api/institutes/profile/with-documents** - Profile updates with document uploads (new)

## Document Upload Endpoint

### Endpoint
```
PUT /api/institutes/profile/with-documents
```

### Authentication
- Requires <PERSON><PERSON> token
- User must have "institute" role

### Content Type
```
Content-Type: multipart/form-data
```

### Request Parameters

#### Profile Fields (all optional)
- `institute_name` (string): Official institute name
- `description` (string): Institute description
- `address` (string): Physical address
- `city` (string): City
- `state` (string): State/Province
- `postal_code` (string): Postal/ZIP code
- `website` (string): Official website URL
- `established_year` (integer): Year established (1800-2024)
- `institute_type` (string): Type of institute (university, college, school, training_center, research_institute, vocational_school, online_platform, other)
- `accreditation` (string): Accreditation details
- `linkedin_url` (string): LinkedIn profile URL
- `facebook_url` (string): Facebook page URL
- `twitter_url` (string): Twitter profile URL
- `logo_url` (string): Logo URL
- `banner_url` (string): Banner URL

#### Document Upload Fields
- `document_files` (array of files): Document files to upload
- `document_types` (array of strings): Types for each document (accreditation, license, certificate, other)
- `document_descriptions` (array of strings, optional): Descriptions for each document

### Example Request

#### Using curl
```bash
curl -X PUT "http://localhost:8000/api/institutes/profile/with-documents" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "institute_name=Example University" \
  -F "description=A leading educational institution" \
  -F "city=New York" \
  -F "state=NY" \
  -F "institute_type=university" \
  -F "established_year=1950" \
  -F "document_files=@accreditation.pdf" \
  -F "document_files=@license.pdf" \
  -F "document_types=accreditation" \
  -F "document_types=license" \
  -F "document_descriptions=University accreditation certificate" \
  -F "document_descriptions=Operating license"
```

#### Using JavaScript/FormData
```javascript
const formData = new FormData();

// Profile fields
formData.append('institute_name', 'Example University');
formData.append('description', 'A leading educational institution');
formData.append('city', 'New York');
formData.append('state', 'NY');
formData.append('institute_type', 'university');
formData.append('established_year', '1950');

// Document uploads
formData.append('document_files', accreditationFile);
formData.append('document_files', licenseFile);
formData.append('document_types', 'accreditation');
formData.append('document_types', 'license');
formData.append('document_descriptions', 'University accreditation certificate');
formData.append('document_descriptions', 'Operating license');

fetch('/api/institutes/profile/with-documents', {
  method: 'PUT',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: formData
});
```

### Response

The API returns the updated institute profile with documents:

```json
{
  "user": {
    "id": "uuid",
    "username": "example_uni",
    "email": "<EMAIL>",
    "mobile": "+**********",
    "country": "USA",
    "profile_picture": null,
    "user_type": "institute",
    "is_email_verified": true,
    "is_mobile_verified": false,
    "created_at": "2024-01-01T00:00:00Z",
    "institute_profile": {
      "id": "uuid",
      "user_id": "uuid",
      "institute_name": "Example University",
      "description": "A leading educational institution",
      "address": null,
      "city": "New York",
      "state": "NY",
      "postal_code": null,
      "website": null,
      "established_year": 1950,
      "institute_type": "university",
      "accreditation": null,
      "is_verified": false,
      "verification_status": "pending",
      "verification_notes": null,
      "verified_at": null,
      "linkedin_url": null,
      "facebook_url": null,
      "twitter_url": null,
      "logo_url": null,
      "banner_url": null,
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z",
      "documents": [
        {
          "id": "uuid",
          "institute_id": "uuid",
          "document_type": "accreditation",
          "document_url": "institute_documents/accreditation/filename.pdf",
          "document_name": "accreditation.pdf",
          "description": "University accreditation certificate",
          "uploaded_at": "2024-01-01T00:00:00Z",
          "verified": false,
          "verified_at": null,
          "verified_by": null
        },
        {
          "id": "uuid",
          "institute_id": "uuid",
          "document_type": "license",
          "document_url": "institute_documents/license/filename.pdf",
          "document_name": "license.pdf",
          "description": "Operating license",
          "uploaded_at": "2024-01-01T00:00:00Z",
          "verified": false,
          "verified_at": null,
          "verified_by": null
        }
      ]
    }
  },
  "profile": {
    // Same as institute_profile above
  },
  "total_competitions": 0,
  "total_mentors": 0,
  "active_competitions": 0,
  "verification_status": "pending"
}
```

## Document Types

The following document types are supported:
- `accreditation`: Accreditation certificates
- `license`: Operating licenses
- `certificate`: Other certificates
- `other`: Other document types

## File Restrictions

- **Allowed formats**: PDF, DOC, DOCX, TXT, RTF, ODT
- **Maximum file size**: 20MB per file
- **Storage location**: Files are stored in `uploads/institute_documents/{type}/` directory

## Separate Document Upload

You can also upload documents separately using:

```
POST /api/upload/institute-document
```

This endpoint accepts:
- `document_type` (form field): Type of document
- `description` (form field, optional): Document description  
- `file` (file): The document file

## Error Responses

- **400 Bad Request**: Invalid document type, file format, or mismatched arrays
- **401 Unauthorized**: Missing or invalid authentication token
- **403 Forbidden**: User is not an institute
- **413 Payload Too Large**: File size exceeds 20MB limit
- **500 Internal Server Error**: Server error during file upload or processing

## UI/UX Updates

### Institute Profile Form Changes
- **Create Button**: Changed from "Create Profile" to "Update Profile" when profile exists
- **Send to Admin for Review Button**: Added alongside the Update button when:
  - Profile exists (not in creation mode)
  - Profile is complete (all required fields filled)
  - Profile status is 'draft' (not already submitted)
- **Button Styling**:
  - Update button: Blue background (`bg-blue-600`)
  - Send to Admin button: Green background (`bg-green-600`)
- **API Integration**: Uses existing `POST /api/institutes/profile/submit-for-verification` endpoint

### Button Behavior
1. **Initial State**: Shows "Create Profile" button for new profiles
2. **After Creation**: Shows "Update Profile" + "Send to Admin for Review" buttons
3. **After Submission**: "Send to Admin for Review" button is hidden, status banner shows "Pending Admin Approval"

## Document Upload Integration

### New Features Added
- **Documents Tab**: Added "Verification Documents" tab to institute profile form
- **Document Upload Component**: `InstituteDocumentUpload.jsx` component for handling file uploads
- **API Integration**: Uses `PUT /api/institutes/profile/with-documents` endpoint when documents are present
- **Fallback Support**: Falls back to regular `PUT /api/institutes/profile` when no new documents

### Document Upload Component Features
- **Drag & Drop**: Support for drag and drop file uploads
- **File Validation**: Validates file types (PDF, DOC, DOCX, TXT, RTF, ODT) and size (20MB max)
- **Document Types**: Categorization (accreditation, license, certificate, other)
- **Document Management**: Add descriptions, view existing documents, remove documents
- **Visual Feedback**: Upload progress, error handling, success states

### Redux Integration
- **New Action**: `saveInstituteProfileWithDocuments` for multipart form data uploads
- **Automatic Detection**: Automatically uses document endpoint when new files are present
- **State Management**: Handles loading states, errors, and success for document uploads

### User Experience
- **Seamless Integration**: Documents tab integrated into existing tabbed interface
- **Clear Instructions**: Help text explaining document requirements and restrictions
- **Existing Documents**: Display and manage previously uploaded documents
- **Verification Status**: Shows verification status for uploaded documents

## API Response Fix

### PROFILE_NOT_CREATED Response Handling
- **Issue Fixed**: API now returns 200 status with `"PROFILE_NOT_CREATED"` error instead of 404
- **Solution**: Updated `fetchInstituteProfile` to check response body for error details
- **Prevention**: Enhanced request prevention logic to stop infinite API calls
- **Compatibility**: Maintains backward compatibility with old 404 error responses

### Changes Made
- **Response Detection**: Checks for `PROFILE_NOT_CREATED` and `PROFILE_NOT_FOUND` in 200 responses
- **State Management**: Properly sets `profileNotFound` state when profile doesn't exist
- **Request Prevention**: Prevents multiple simultaneous requests by checking loading state
- **Error Handling**: Comprehensive error handling for both new and old API response formats

## Form Validation & UX Improvements

### Enhanced Profile Creation Experience
- **Mandatory Field Validation**: Create Profile button disabled until all required fields are filled
- **Real-time Validation**: Field-level validation with error messages and visual feedback
- **Progress Tracking**: Visual progress bar showing completion percentage
- **Smart Button States**: Update Profile can be clicked anytime, Create Profile requires validation

### Validation Features
- **Required Fields**: Institute Name, Institute Type, Description, Address, City
- **Visual Indicators**: Red asterisks (*) for required fields, error borders for invalid fields
- **Error Messages**: Contextual error messages below each field
- **Progress Bar**: Shows completion percentage with color-coded feedback
- **Helpful Guidance**: Clear instructions and field completion status

### User Experience Enhancements
- **Conditional Button States**:
  - Create Profile: Disabled until all mandatory fields are completed
  - Update Profile: Always enabled for existing profiles
- **Visual Feedback**: Error states, progress indicators, and completion status
- **Accessibility**: Proper ARIA labels, tooltips, and keyboard navigation
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Notes

- All profile fields are optional - you can update only the fields you want to change
- Document arrays (files, types, descriptions) must have matching lengths
- Documents are not verified by default - admin verification is required
- The original JSON-only endpoint (`PUT /api/institutes/profile`) remains available for profile updates without documents
